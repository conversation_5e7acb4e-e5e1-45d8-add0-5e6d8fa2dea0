use winit::{
    application::<PERSON><PERSON><PERSON><PERSON>,
    event::WindowEvent,
    event_loop::{ActiveEventLoop, EventLoop},
    window::{Window, WindowId, WindowAttributes},
};

struct App {
    window: Option<Window>,
}

impl ApplicationHandler for App {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.window.is_none() {
            let window_attributes = WindowAttributes::default()
                .with_title("Simple Rust Window")
                .with_inner_size(winit::dpi::LogicalSize::new(800, 600));

            let window = event_loop.create_window(window_attributes).unwrap();
            self.window = Some(window);
            println!("Window created! Press Alt+F4 or close button to exit.");
        }
    }

    fn window_event(&mut self, event_loop: &ActiveEventLoop, _window_id: WindowId, event: WindowEvent) {
        match event {
            WindowEvent::CloseRequested => {
                println!("Window close requested. Exiting...");
                event_loop.exit();
            }
            WindowEvent::Resized(physical_size) => {
                println!("Window resized to: {}x{}", physical_size.width, physical_size.height);
            }
            WindowEvent::RedrawRequested => {
                // Here you would typically draw to the window
                // For now, we'll just acknowledge the redraw request
            }
            _ => (),
        }
    }
}

fn main() {
    let event_loop = EventLoop::new().unwrap();
    let mut app = App { window: None };

    event_loop.run_app(&mut app).unwrap();
}
